<template>
  <ConfirmDialog
    ref="dialogRef"
    title="销假申请"
    confirm-text="提交销假"
    cancel-text="取消"
    @confirm="handleSubmit"
    @cancel="handleCancel"
  >
    <view class="leave-return-content">
      <!-- 请假信息展示 -->
      <view class="leave-info-section mb-[32rpx]">
        <view class="section-title mb-[24rpx]">请假信息</view>

        <view class="info-item">
          <text class="info-label">请假类别：</text>
          <text class="info-value">{{ leaveInfo.leaveCategory || '晚寝请假' }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">请假人：</text>
          <text class="info-value">{{ leaveInfo.qjrxm || '陶正权' }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">请假类型：</text>
          <text class="info-value">{{ leaveInfo.qjlxmc || '事假' }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">是否出校：</text>
          <text class="info-value">{{ leaveInfo.sfsqcxm ? '是' : '否' }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">请假时间：</text>
          <text class="info-value">{{ formatLeaveTime(leaveInfo.qjkssj, leaveInfo.qjjssj) }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">请假时长：</text>
          <text class="info-value">{{ leaveInfo.qjss || 336 }}小时</text>
        </view>

        <view class="info-item">
          <text class="info-label">请假天数：</text>
          <text class="info-value">{{ leaveInfo.qjts || 14 }}天</text>
        </view>
      </view>

      <!-- 销假表单 -->
      <view class="return-form-section">
        <view class="section-title mb-[24rpx]">销假信息</view>

        <!-- 销假说明 -->
        <view class="form-item mb-[24rpx]">
          <view class="form-label mb-[12rpx]">
            <text>销假说明</text>
            <text class="text-required">*</text>
          </view>
          <TextareaInput
            v-model="returnForm.xjsm"
            placeholder="请输入销假说明..."
            :maxlength="500"
            :show-count="true"
            :auto-height="true"
          />
        </view>

        <!-- 销假附件 -->
        <view class="form-item">
          <view class="form-label mb-[12rpx]">
            <text>销假附件</text>
          </view>
          <FileUploader
            v-model="returnForm.attachments"
            upload-type="leave-return"
            title=""
            :show-title="false"
            tip-text="支持上传相关证明材料"
            empty-text="暂无附件"
            :count="5"
          />
        </view>
      </view>
    </view>
  </ConfirmDialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import ConfirmDialog from './ConfirmDialog.vue'
import TextareaInput from './TextareaInput.vue'
import FileUploader from './FileUploader.vue'
import type { LeaveRecordItem } from '@/types/leave'

// Props定义
interface Props {
  /** 请假信息 */
  leaveInfo?: Partial<LeaveRecordItem>
}

// 事件定义
interface Emits {
  (
    e: 'submit',
    data: {
      id: number
      wqsq: number
      qjlx: string
      sfsqcxm: number
      qjss: number
      xjsm: string
      fjlb2: string
    },
  ): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  leaveInfo: () => ({}),
})

const emit = defineEmits<Emits>()

// 弹窗引用
const dialogRef = ref<InstanceType<typeof ConfirmDialog>>()

// 销假表单数据
const returnForm = reactive({
  xjsm: '', // 销假说明
  attachments: [] as Array<{ url: string; name: string }>, // 销假附件
})

// 格式化请假时间显示
const formatLeaveTime = (startTime?: string, endTime?: string) => {
  if (!startTime || !endTime) {
    return '2025-07-10 00:00:00 至 2025-07-24 00:00:00'
  }
  return `${startTime} 至 ${endTime}`
}

// 显示弹窗
const show = () => {
  // 重置表单
  returnForm.xjsm = ''
  returnForm.attachments = []
  dialogRef.value?.show()
}

// 隐藏弹窗
const hide = () => {
  dialogRef.value?.hide()
}

// 处理提交
const handleSubmit = () => {
  // 验证必填字段
  if (!returnForm.xjsm.trim()) {
    uni.showToast({
      title: '请输入销假说明',
      icon: 'none',
    })
    return
  }

  // 验证请假信息是否存在
  if (!props.leaveInfo || !props.leaveInfo.id) {
    uni.showToast({
      title: '请假信息不完整',
      icon: 'none',
    })
    return
  }

  // 处理附件列表，转换为字符串格式
  const fjlb2 = returnForm.attachments.map((file) => `${file.url}|${file.name}`).join('|')

  // 提交数据
  emit('submit', {
    id: props.leaveInfo.id!,
    wqsq: props.leaveInfo.wqsq || 1,
    qjlx: props.leaveInfo.qjlx || '1',
    sfsqcxm: props.leaveInfo.sfsqcxm || 0,
    qjss: props.leaveInfo.qjss || 0,
    xjsm: returnForm.xjsm.trim(),
    fjlb2,
  })
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
}

// 对外暴露方法
defineExpose({
  show,
  hide,
})
</script>

<style lang="scss" scoped>
.leave-return-content {
  max-height: 60vh;
  overflow-y: auto;
}

.section-title {
  padding-bottom: 12rpx;
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  border-bottom: 2rpx solid #e5e5e5;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  line-height: 1.5;
}

.info-label {
  min-width: 160rpx;
  font-weight: 500;
  color: #666;
}

.info-value {
  flex: 1;
  color: #333;
  word-break: break-all;
}

.form-item {
  // 表单项样式
}

.form-label {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.text-required {
  margin-left: 4rpx;
  color: #ff4d4f;
}
</style>
